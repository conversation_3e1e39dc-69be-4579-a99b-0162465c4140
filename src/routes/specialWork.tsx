import {
  GasIntervalPage,
  JobSliceIntervalPage,
  SpecialWorkConfigPage,
} from "pages/specialWork";
import { SpecialWorkIndexPage } from "pages/specialWork/specialWorkIndexPage";
import {
  AppointmentProcessTemplatePage,
  CodeConfigPage,
  CreateTicketPage,
  GasStandardPage,
  JobAppoimentPage,
  JsTemplateUserPage,
  ProcessTemplatePage,
  RiskMeasurePage,
  SafetyAnalysisPage,
  SafetyDisclosurePage,
  SafetyMeasurePage,
  TicketListPage,
} from "pages/ticket";
import { SpecialWorkRoutes } from "utils/routerConstants";
import { generateLoader, type ChildrenMap } from "utils/routeUtils";

/**
 * 作业安全措施相关路由配置
 * 包含作业安全分析库、作业安全措施库、风险辨识措施库等
 */
export const SafetyMeasureMap: ChildrenMap = generateLoader(
  [
    {
      element: <SafetyAnalysisPage />,
      name: "作业安全分析库",
      path: SpecialWorkRoutes.SAFETY_ANALYSIS,
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
    {
      element: <SafetyMeasurePage />,
      name: "作业安全措施库",
      path: SpecialWorkRoutes.SAFETY_MEASURE,
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      element: <RiskMeasurePage />,
      name: "风险辨识措施库",
      path: SpecialWorkRoutes.RISK_MEASURE,
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      element: <GasStandardPage />,
      name: "气体采样分析库",
      path: SpecialWorkRoutes.GAS_STANDARD,
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      element: <SafetyDisclosurePage />,
      name: "安全交底清单",
      path: SpecialWorkRoutes.SAFETY_DISCLOSURE,
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
  ],
  1100
);

/**
 * 作业模板管理相关路由配置
 * 包含作业预约、作业票等功能
 */
export const JobTmplMap: ChildrenMap = generateLoader(
  [
    {
      element: <JobAppoimentPage />,
      name: "作业预约",
      path: SpecialWorkRoutes.JOB_APPOIMENT,
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "view",
          name: "查看",
        },
        {
          action: "approve",
          name: "审批",
        },
      ],
    },
    {
      element: <TicketListPage />,
      name: "作业票",
      path: SpecialWorkRoutes.JOB_TMPL_LIST,
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "view",
          name: "查看打印",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
    {
      element: <CreateTicketPage />,
      name: "创建作业票",
      hide: true,
      path: `${SpecialWorkRoutes.JOB_TMPL}/:cid/:id`,
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
  ],
  1200
);

/**
 * 作业票配置相关路由配置
 * 包含作业管理、作业票通用配置、模板配置等
 */
export const Ticket: ChildrenMap = generateLoader(
  [
    {
      path: SpecialWorkRoutes.DASHBOARD,
      element: <SpecialWorkIndexPage />,
      name: "作业管理",
    },
    {
      path: SpecialWorkRoutes.SW_CONFIG,
      element: <SpecialWorkConfigPage />,
      name: "作业票通用配置",
    },
    {
      path: SpecialWorkRoutes.JS_TEMPLATE_USER,
      element: <JsTemplateUserPage />,
      name: "作业票模板配置",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: `${SpecialWorkRoutes.APPOINTMENT_PROCESS_TEMPLATE}`,
      element: <AppointmentProcessTemplatePage />,
      name: "作业预约配置",
      meta: [
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: SpecialWorkRoutes.CODE_CONFIG,
      element: <CodeConfigPage />,
      name: "作业票编码配置",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: `${SpecialWorkRoutes.PROCESS_TEMPLATE}/:id`,
      element: <ProcessTemplatePage />,
      hide: true,
      name: "作业票审批配置",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: SpecialWorkRoutes.GAS_INTERVAL,
      element: <GasIntervalPage />,
      name: "气体检测配置",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: SpecialWorkRoutes.JOB_SLICE_INTERVAL,
      element: <JobSliceIntervalPage />,
      name: "作业票有效期",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
  ],
  1300
);
