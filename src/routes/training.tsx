// 从 coporateTraining 目录导入企业培训相关组件
import { CoporateTrainingCertificatePage } from "pages/coporateTraining/certificatePage";
import { TrainingConfigPage } from "pages/coporateTraining/configPage";
import { CoursePage } from "pages/coporateTraining/coursePage";
import { CoursewarePage } from "pages/coporateTraining/coursewarePage";
import { PaperPage } from "pages/coporateTraining/paperPage";
import { CoporateTrainingPeopleExamRecordPage } from "pages/coporateTraining/peopleExamRecordPage";
import { CoporateTrainingPeoplePage } from "pages/coporateTraining/peoplePage";
import { CoporateTrainingPeopleStudyRecordPage } from "pages/coporateTraining/peopleStudyRecordPage";
import { CoporateTrainingPlanPage } from "pages/coporateTraining/planPage";
import { QuestionPage } from "pages/coporateTraining/questionPage";
import { CoporateTrainingRecordPage } from "pages/coporateTraining/recordPage";
import { TrainingSubjectPage } from "pages/coporateTraining/subjectPage";
import { TeacherPage } from "pages/coporateTraining/teacherPage";

// 从 training 目录导入培训相关组件
import { MyTrainingCertificatePage } from "pages/training/certificatePage";
import { TrainingCoursePage } from "pages/training/coursePage";
import { MyTrainingPage } from "pages/training/myPage";
import { TrainingRecordPage } from "pages/training/recordPage";
import { TrainingIndexPage } from "pages/training/trainingIndexPage";
import { TrainingLayoutPage } from "pages/training/trainingPage";

import { TrainingRoutes } from "utils/routerConstants";
import { generateLoader, type ChildrenMap } from "utils/routeUtils";

/**
 * 培训教育材料相关路由配置
 */
export const TrainingMaterialMap: ChildrenMap = generateLoader(
  [
    {
      path: TrainingRoutes.TRAINING_DASHBOARD,
      element: <TrainingIndexPage />,
      name: "培训教育",
    },
    {
      path: TrainingRoutes.TRAINING_CONFIG,
      element: <TrainingConfigPage />,
      name: "学习资料通用配置",
    },
    {
      path: TrainingRoutes.TRAINING_SUBJECT,
      element: <TrainingSubjectPage />,
      name: "知识科目分类",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: TrainingRoutes.TRAINING_TEACHER,
      element: <TeacherPage />,
      name: "讲师管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: TrainingRoutes.TRAINING_COURSEWARE,
      element: <CoursewarePage />,
      name: "课件库",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "approve",
          name: "通过",
        },
        {
          action: "reject",
          name: "驳回",
        },
        {
          action: "batchApprove",
          name: "批量通过",
        },
        {
          action: "batchReject",
          name: "批量驳回",
        },
      ],
    },
    {
      path: TrainingRoutes.TRAINING_QUESTION,
      element: <QuestionPage />,
      name: "试题库",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "approve",
          name: "通过",
        },
        {
          action: "reject",
          name: "驳回",
        },
        {
          action: "batchApprove",
          name: "批量通过",
        },
        {
          action: "batchReject",
          name: "批量驳回",
        },
        {
          action: "import",
          name: "导入",
        },
      ],
    },
    {
      path: TrainingRoutes.TRAINING_PAPER,
      element: <PaperPage />,
      name: "试卷管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: TrainingRoutes.TRAINING_COURSE,
      element: <CoursePage />,
      name: "课程管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
  ],
  600
);

/**
 * 培训管理相关路由配置
 */
export const TrainingManagementMap: ChildrenMap = generateLoader(
  [
    {
      path: TrainingRoutes.TRAINING_CERTIFICATE,
      element: <CoporateTrainingCertificatePage />,
      name: "培训证书管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: TrainingRoutes.TRAINING_PLAN,
      element: <CoporateTrainingPlanPage />,
      name: "培训计划",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "detail",
          name: "查看详情",
        },
        {
          action: "share",
          name: "分享",
        },
        {
          action: "revokeEntry",
          name: "取消入厂资格",
        },
      ],
    },
    {
      path: TrainingRoutes.TRAINING_RECORD,
      element: <CoporateTrainingRecordPage />,
      name: "培训记录",
      meta: [
        {
          action: "export",
          name: "导出",
        },
      ],
    },
  ],
  600
);

/**
 * 学员管理相关路由配置
 */
export const StudentManagementMap: ChildrenMap = generateLoader(
  [
    {
      path: TrainingRoutes.PEOPLE_STUDY_RECORD,
      element: <CoporateTrainingPeopleStudyRecordPage />,
      name: "学习记录",
    },
    {
      path: TrainingRoutes.PEOPLE_EXAM_RECORD,
      element: <CoporateTrainingPeopleExamRecordPage />,
      name: "考试记录",
      meta: [
        {
          action: "detail",
          name: "详情",
        },
      ],
    },
    {
      path: TrainingRoutes.PEOPLE,
      element: <CoporateTrainingPeoplePage />,
      name: "学员培训档案",
    },
  ],
  700
);

/**
 * 我的培训相关路由配置
 */
export const MyTrainingMap: ChildrenMap = generateLoader(
  [
    {
      index: true,
      element: <MyTrainingPage />,
      name: "培训计划",
    },
    {
      path: "training_record/:record_id",
      element: <TrainingRecordPage />,
      name: "培训详情",
      handle: {
        crumb: {
          name: "培训详情",
        },
      },
    },
    {
      path: "training_record/:record_id/course/:id",
      element: <TrainingCoursePage />,
      name: "课程详情",
      handle: {
        crumb: {
          history: {
            name: "培训详情",
            path: "/my_training/training_record/",
            params: "record_id",
          },
          name: "课程详情",
        },
      },
    },
  ],
  800
);

/**
 * 我的培训证书相关路由配置
 */
export const MyTrainingCertificateMap: ChildrenMap = generateLoader(
  [
    {
      index: true,
      path: "certificate",
      element: <MyTrainingCertificatePage />,
      name: "培训证书",
    },
  ],
  900
);

/**
 * 我的培训布局相关路由配置
 */
export const MyTrainingLayoutMap: ChildrenMap = generateLoader(
  [
    {
      path: TrainingRoutes.MY_TRAINING,
      element: <TrainingLayoutPage />,
      name: "我的培训",
      handle: {
        crumb: {
          home: true,
          name: "我的培训",
        },
      },
    },
  ],
  1000
);
