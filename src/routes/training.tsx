// 从 coporateTraining 统一导出导入企业培训相关组件
import {
  CoporateTrainingCertificatePage,
  CoporateTrainingPeopleExamRecordPage,
  CoporateTrainingPeoplePage,
  CoporateTrainingPeopleStudyRecordPage,
  CoporateTrainingPlanPage,
  CoporateTrainingRecordPage,
  CoursePage,
  CoursewarePage,
  PaperPage,
  QuestionPage,
  TeacherPage,
  TrainingConfigPage,
  TrainingSubjectPage,
} from "pages/coporateTraining";

// 从 training 目录导入具体文件
import { TrainingIndexPage } from "pages/training/trainingIndexPage";
import { TrainingLayoutPage } from "pages/training/trainingPage";

import { TrainingRoutes } from "utils/routerConstants";
import { generateLoader, type ChildrenMap } from "utils/routeUtils";

/**
 * 培训教育材料相关路由配置
 */
export const TrainingMaterialMap: ChildrenMap = generateLoader(
  [
    {
      path: TrainingRoutes.TRAINING_DASHBOARD,
      element: <TrainingIndexPage />,
      name: "培训教育",
    },
    {
      path: TrainingRoutes.TRAINING_CONFIG,
      element: <TrainingConfigPage />,
      name: "学习资料通用配置",
    },
    {
      path: TrainingRoutes.TRAINING_SUBJECT,
      element: <TrainingSubjectPage />,
      name: "知识科目分类",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: TrainingRoutes.TRAINING_TEACHER,
      element: <TeacherPage />,
      name: "讲师管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: TrainingRoutes.TRAINING_COURSEWARE,
      element: <CoursewarePage />,
      name: "课件库",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "approve",
          name: "通过",
        },
        {
          action: "reject",
          name: "驳回",
        },
        {
          action: "batchApprove",
          name: "批量通过",
        },
        {
          action: "batchReject",
          name: "批量驳回",
        },
      ],
    },
    {
      path: TrainingRoutes.TRAINING_QUESTION,
      element: <QuestionPage />,
      name: "试题库",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "approve",
          name: "通过",
        },
        {
          action: "reject",
          name: "驳回",
        },
        {
          action: "batchApprove",
          name: "批量通过",
        },
        {
          action: "batchReject",
          name: "批量驳回",
        },
        {
          action: "import",
          name: "导入",
        },
      ],
    },
    {
      path: TrainingRoutes.TRAINING_PAPER,
      element: <PaperPage />,
      name: "试卷管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: TrainingRoutes.TRAINING_COURSE,
      element: <CoursePage />,
      name: "课程管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
  ],
  600
);

/**
 * 培训管理相关路由配置
 */
export const TrainingManagementMap: ChildrenMap = generateLoader(
  [
    {
      path: TrainingRoutes.TRAINING_CERTIFICATE,
      element: <CoporateTrainingCertificatePage />,
      name: "培训证书管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: TrainingRoutes.TRAINING_PLAN,
      element: <CoporateTrainingPlanPage />,
      name: "培训计划",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "detail",
          name: "查看详情",
        },
        {
          action: "share",
          name: "分享",
        },
        {
          action: "revokeEntry",
          name: "取消入厂资格",
        },
      ],
    },
    {
      path: TrainingRoutes.TRAINING_RECORD,
      element: <CoporateTrainingRecordPage />,
      name: "培训记录",
      meta: [
        {
          action: "export",
          name: "导出",
        },
      ],
    },
  ],
  600
);

/**
 * 学员管理相关路由配置
 */
export const StudentManagementMap: ChildrenMap = generateLoader(
  [
    {
      path: TrainingRoutes.PEOPLE_STUDY_RECORD,
      element: <CoporateTrainingPeopleStudyRecordPage />,
      name: "学习记录",
    },
    {
      path: TrainingRoutes.PEOPLE_EXAM_RECORD,
      element: <CoporateTrainingPeopleExamRecordPage />,
      name: "考试记录",
      meta: [
        {
          action: "detail",
          name: "详情",
        },
      ],
    },
    {
      path: TrainingRoutes.PEOPLE,
      element: <CoporateTrainingPeoplePage />,
      name: "学员培训档案",
    },
  ],
  700
);

/**
 * 我的培训布局相关路由配置
 */
export const MyTrainingLayoutMap: ChildrenMap = generateLoader(
  [
    {
      path: TrainingRoutes.MY_TRAINING,
      element: <TrainingLayoutPage />,
      name: "我的培训",
      handle: {
        crumb: {
          home: true,
          name: "我的培训",
        },
      },
    },
  ],
  1000
);
