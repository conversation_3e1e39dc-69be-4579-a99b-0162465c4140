/**
 * 路由配置统一导出文件
 *
 * 按照 useMenuHooks.tsx 中的路由分组导出所有 Map
 */

// 特殊作业相关路由
export { JobTmplMap, SafetyMeasureMap, Ticket } from "./specialWork";

// 双重预防机制相关路由
export {
  BbTaskMap,
  CheckPlanMap,
  EnterpriseSelfInspectionMap,
  GovSupervisionMap,
  IncentiveMap,
  ManageCardMap,
  RiskMap,
  SnapMap,
} from "./doubleGuard";

// 重大危险源相关路由
export { DataVisulizationMap, OnlineMonitorAlertMap } from "./majorHazard";

// 基础信息管理相关路由
export {
  EnterpriseCertificateMap,
  EnterpriseInformationMap,
  EquipmentFacilitiesMap,
  ProductiveProcessMap,
} from "./baseSetting";

// 系统设置相关路由
export { SystemMap } from "./systemSetting";

// 培训管理相关路由
export {
  MyTrainingLayoutMap,
  StudentManagementMap,
  TrainingManagementMap,
  TrainingMaterialMap,
} from "./training";

// 承包商管理相关路由
export {
  ContractorBasicMap,
  ContractorEntryMap,
  ContractorEvaluationMap,
  ContractorProjectMap,
} from "./contractor";

// TODO: 需要继续添加以下路由的导出
// - IntelligentInspectionMap (智能巡检)
// - EmergencyManagementMap (应急管理)
// - HumanResourceMap, BasicContractorMap, DocumentManagmentMap (基础信息管理的其他部分)
// - PersonnelLocationMap (人员定位)
// - AIMap (AI相关)
// - EquipmentArchiveMap, EquipmentManagementMap (设备管理)
// - AlarmSettingsMap, AlarmProcessMap, AlarmAnalysisMap (报警管理)
// - HiddenMenu (隐藏菜单)
