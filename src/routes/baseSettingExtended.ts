import {
  BasicInfoDocumentCategoryPage,
  BasicInfoDocumentInformationPage,
  CertificatePage,
  ChemicalPage,
  ContractorAccidentPage,
  ContractorCertificatePage,
  ContractorEmployeePage,
  ContractorPage,
  ContractorProjectPage,
  DangerousProcessPage,
  DepartmentPage,
  EmployeePage,
  EquipmentPage,
  GroupPage,
  InterlockPage,
  MajorHazardPage,
  PositionPage,
  ProductionUnitPage,
  RolePage,
  StorageRecordPage,
  StorageTankAreaPage,
  StorageTankPage,
  ToxicFlammableGasPage,
  WarehouseAreaPage,
  WarehousePage,
} from "pages/basicInfo";
import { ContractorEmployeeCertificatePage } from "pages/basicInfo/contractorEmployeeCertificatePage";
import { EquipmentManagementEquipmentCategoryPage } from "pages/equipmentManagement";
import { BasicRoutes } from "utils/routerConstants";
import { generateLoader, type ChildrenMap } from "utils/routeUtils";

/**
 * 生产过程基础信息路由配置
 */
export const ProductiveProcessMap: ChildrenMap[] = generateLoader(
  [
    {
      path: BasicRoutes.MAJOR_HAZARD,
      element: <MajorHazardPage />,
      name: "重大危险源信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "evaluate",
          name: "等级评定",
        },
      ],
    },
    {
      path: BasicRoutes.TOXIC_FLAMMABLE_GAS,
      element: <ToxicFlammableGasPage />,
      name: "有毒可燃气体信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: BasicRoutes.DANGEROUS_PROCESS,
      element: <DangerousProcessPage />,
      name: "危险化工工艺信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "import",
          name: "导入",
        },
      ],
    },
    {
      path: BasicRoutes.PRODUCTION_UNIT,
      element: <ProductionUnitPage />,
      name: "生产装置信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "import",
          name: "导入",
        },
      ],
    },
    {
      path: BasicRoutes.STORAGE_TANK_AREA,
      element: <StorageTankAreaPage />,
      name: "储罐区信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: BasicRoutes.STORAGE_TANK,
      element: <StorageTankPage />,
      name: "储罐信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "import",
          name: "导入",
        },
      ],
    },
    {
      path: BasicRoutes.WAREHOUSE_AREA,
      element: <WarehouseAreaPage />,
      name: "库区信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: BasicRoutes.WAREHOUSE,
      element: <WarehousePage />,
      name: "仓库信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "import",
          name: "导入",
        },
      ],
    },
    {
      path: BasicRoutes.CHEMICAL,
      element: <ChemicalPage />,
      name: "化学品信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "import",
          name: "导入",
        },
      ],
    },
    {
      path: BasicRoutes.STORAGE_RECORD,
      element: <StorageRecordPage />,
      name: "化学品出入库记录",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: BasicRoutes.INTERLOCK,
      element: <InterlockPage />,
      name: "联锁信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
  ],
  600
);

/**
 * 设备设施基础信息路由配置
 */
export const EquipmentFacilitiesMap: ChildrenMap[] = generateLoader(
  [
    {
      path: BasicRoutes.EQUIPMENT_CATEGORY,
      element: <EquipmentManagementEquipmentCategoryPage />,
      name: "设备类型",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
      ],
    },
    {
      path: BasicRoutes.EQUIPMENT,
      element: <EquipmentPage />,
      name: "设备信息",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "detail",
          name: "查看详情",
        },
        {
          action: "import",
          name: "导入",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
  ],
  700
);
