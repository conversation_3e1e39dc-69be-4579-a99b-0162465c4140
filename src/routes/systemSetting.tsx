import {
  AboutPage,
  ActionRecordPage,
  CalendarPage,
  DicPage,
  LoginRecordPage,
} from "pages/basicInfo";
import { DisplayCategoryPage } from "pages/majorHazard";
import {
  AlarmPushConfigPage,
  NoticePushConfigPage,
  OmConfigPage,
} from "pages/systemSettings";
import { SystemSettingsRoutes } from "utils/routerConstants";
import { generateLoader, type ChildrenMap } from "utils/routeUtils";

/**
 * 系统设置相关路由配置
 */
export const SystemMap: ChildrenMap[] = generateLoader(
  [
    {
      path: SystemSettingsRoutes.OM_CONFIG,
      element: <OmConfigPage />,
      name: "后台配置",
    },
    {
      path: SystemSettingsRoutes.BIGSCREEN_LEGEND,
      element: <DisplayCategoryPage />,
      name: "大屏图例管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: SystemSettingsRoutes.DIC,
      element: <DicPage />,
      name: "字典管理",
      meta: [
        {
          action: "editDicValue",
          name: "字典值",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
      ],
    },
    {
      path: SystemSettingsRoutes.NOTICE_PUSH_CONFIG,
      element: <NoticePushConfigPage />,
      name: "提醒推送配置",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: SystemSettingsRoutes.ALARM_PUSH_CONFIG,
      element: <AlarmPushConfigPage />,
      name: "报警规则配置",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: SystemSettingsRoutes.LOGINRECORDS,
      element: <LoginRecordPage />,
      name: "登录记录",
    },
    {
      path: SystemSettingsRoutes.ACTIONRECORDS,
      element: <ActionRecordPage />,
      name: "操作记录",
    },
    {
      path: SystemSettingsRoutes.CALENDAR,
      element: <CalendarPage />,
      name: "工作日历",
      meta: [
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: SystemSettingsRoutes.ABOUT,
      element: <AboutPage />,
      name: "关于系统",
    },
  ],
  1000
);
