import { RoleKeyPath } from "config";
import type { RouteObject } from "react-router-dom";

/**
 * 路由映射类型定义
 * 扩展了 RouteObject，增加了业务相关的属性
 */
export type ChildrenMap = (RouteObject & {
  name: string;
  index?: boolean;
  path?: string;
  meta?: any[];
  hide?: boolean;
})[];

/**
 * 生成带有权限加载器的路由配置
 * @param list 路由配置列表
 * @param order 排序基数
 * @returns 带有权限加载器的路由配置列表
 */
export const generateLoader = (
  list: ChildrenMap[],
  order: number
): ChildrenMap[] => {
  const addLoader: ChildrenMap = [];
  list.forEach((index: any, k) => {
    addLoader.push({
      ...index,
      loader: () => {
        const temporary = [];
        (index?.meta ?? []).forEach((o: any) => {
          temporary.push({
            // 这里的code是拿来关联path的
            code: `${index[RoleKeyPath]}${o?.action}`,
            name: o?.name,
            description: "按钮权限",
            requestType: 1,
            sort: Number.parseInt(order) + Number.parseInt(k),
            clientPath: `${index?.[RoleKeyPath]}_${o?.action}`,
            menuId: index[RoleKeyPath],
          });
        });
        return temporary;
      },
    });
  });
  return addLoader;
};
