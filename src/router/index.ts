import {
  BbTaskMap,
  CheckPlanMap,
  EnterpriseSelfInspectionMap,
  GovSupervisionMap,
  IncentiveMap,
  ManageCardMap,
  RiskMap,
  SnapMap,
} from "./configs/doubleGuard";

// 暂时导出双重预防机制相关的Map，后续会添加其他模块
export const getAllRouterMaps = () => {
  return [
    ...RiskMap,
    ...ManageCardMap,
    ...BbTaskMap,
    ...CheckPlanMap,
    ...IncentiveMap,
    ...GovSupervisionMap,
    ...SnapMap,
    ...EnterpriseSelfInspectionMap,
  ];
};

// 后续会完善这个函数
export const createRoleRouter = () => {
  return getAllRouterMaps();
};
